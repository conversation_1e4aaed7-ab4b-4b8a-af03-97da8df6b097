/**
 * Simulation Enhancer CSS
 * Styles for enhanced multi-sensory simulations
 */

/* Step Navigation */
.step-navigation {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  margin: 0 auto;
  width: 80%;
  max-width: 600px;
  z-index: 1000;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: opacity 0.3s ease;
}

.step-navigation:hover {
  opacity: 1 !important;
}

.step-nav-btn {
  background-color: #333;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.step-nav-btn:hover:not(:disabled) {
  transform: scale(1.05);
  background-color: #444;
}

.step-nav-btn:active:not(:disabled) {
  transform: scale(0.95);
}

.step-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.next-btn {
  background-color: #fe2c55;
}

.next-btn:hover:not(:disabled) {
  background-color: #ff4d79;
}

.step-indicator {
  flex-grow: 1;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.step-title {
  position: absolute;
  top: -30px;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Step Content */
[data-step] {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

[data-step].entering {
  opacity: 0;
  transform: translateX(20px);
}

[data-step].active {
  opacity: 1;
  transform: translateX(0);
}

[data-step].exiting {
  opacity: 0;
  transform: translateX(-20px);
}

/* Interactive Elements */
.interactive, [data-interactive], 
button, input[type="range"], input[type="checkbox"], select {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.interactive:hover, [data-interactive]:hover,
button:hover, input[type="range"]:hover, input[type="checkbox"]:hover, select:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.interactive:active, [data-interactive]:active,
button:active, input[type="range"]:active, input[type="checkbox"]:active, select:active {
  transform: scale(0.98);
}

/* Highlight Effect */
.highlight-effect {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  background: radial-gradient(circle, rgba(254, 44, 85, 0.4) 0%, rgba(254, 44, 85, 0) 70%);
  transform: translate(-50%, -50%);
  z-index: 9999;
  animation: highlight-pulse 1s ease-out forwards;
}

@keyframes highlight-pulse {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* Accessibility */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Styles */
:focus {
  outline: 2px solid #fe2c55 !important;
  outline-offset: 2px !important;
}

/* Audio Controls */
.audio-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  z-index: 1000;
}

.audio-btn {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.audio-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

/* Narration Captions */
.narration-caption {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 14px;
  max-width: 80%;
  text-align: center;
  z-index: 999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.narration-caption.active {
  opacity: 1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .step-navigation {
    width: 90%;
    padding: 8px;
  }
  
  .step-nav-btn {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .step-indicator {
    font-size: 12px;
  }
  
  .step-title {
    font-size: 14px;
    top: -25px;
  }
  
  .narration-caption {
    font-size: 12px;
    max-width: 90%;
  }
}
