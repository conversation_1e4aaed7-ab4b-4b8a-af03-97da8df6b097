/**
 * <PERSON><PERSON><PERSON> to update import statements in JavaScript files after restructuring
 * This script will scan all JavaScript files and update import paths
 * to point to the new locations of JavaScript files
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// Map of old file paths to new file paths (without the 'js/' prefix)
const filePathMap = {
  // API files
  'api-optimization.js': 'api/api-optimization.js',
  'api-settings.js': 'api/api-settings.js',
  'apiSettingsManager.js': 'api/apiSettingsManager.js',
  'gemini-api.js': 'api/gemini-api.js',
  'googleGenerativeAI.js': 'api/googleGenerativeAI.js',
  
  // Alarm files
  'alarm-data-service.js': 'alarms/alarm-data-service.js',
  'alarm-handler.js': 'alarms/alarm-handler.js',
  'alarm-mini-display.js': 'alarms/alarm-mini-display.js',
  'alarm-service.js': 'alarms/alarm-service.js',
  'alarm-service-worker.js': 'alarms/alarm-service-worker.js',
  
  // Calendar files
  'calendarManager.js': 'calendar/calendarManager.js',
  'calendar-views.js': 'calendar/calendar-views.js',
  
  // Common files
  'common.js': 'common/common.js',
  'common-header.js': 'common/common-header.js',
  'inject-header.js': 'common/inject-header.js',
  'transitionManager.js': 'common/transitionManager.js',
  'theme-manager.js': 'common/theme-manager.js',
  'themeManager.js': 'common/themeManager.js',
  'clock-display.js': 'common/clock-display.js',
  
  // Data files
  'cacheManager.js': 'data/cacheManager.js',
  'cross-tab-sync.js': 'data/cross-tab-sync.js',
  'data-loader.js': 'data/data-loader.js',
  'dataStorage.js': 'data/dataStorage.js',
  'data-sync-integration.js': 'data/data-sync-integration.js',
  'data-sync-manager.js': 'data/data-sync-manager.js',
  'indexedDB.js': 'data/indexedDB.js',
  'storageManager.js': 'data/storageManager.js',
  
  // Academic details files
  'semester-management.js': 'features/academic-details/semester-management.js',
  'subject-management.js': 'features/academic-details/subject-management.js',
  
  // AI files
  'ai-latex-conversion.js': 'features/ai/ai-latex-conversion.js',
  'ai-researcher.js': 'features/ai/ai-researcher.js',
  'simulation-enhancer.js': 'features/ai/simulation-enhancer.js',
  
  // Flashcards files
  'flashcardManager.js': 'features/flashcards/flashcardManager.js',
  'flashcards.js': 'features/flashcards/flashcards.js',
  'flashcardTaskIntegration.js': 'features/flashcards/flashcardTaskIntegration.js',
  'sm2.js': 'features/flashcards/sm2.js',
  
  // Marks tracking files
  'marks-tracking.js': 'features/marks-tracking/marks-tracking.js',
  'subject-marks.js': 'features/marks-tracking/subject-marks.js',
  'subject-marks-integration.js': 'features/marks-tracking/subject-marks-integration.js',
  'subject-marks-ui.js': 'features/marks-tracking/subject-marks-ui.js',
  'weightage-connector.js': 'features/marks-tracking/weightage-connector.js',
  
  // Pomodoro files
  'pomodoroGlobal.js': 'features/pomodoro/pomodoroGlobal.js',
  'pomodoroTimer.js': 'features/pomodoro/pomodoroTimer.js',
  
  // Priority files
  'priority-calculator.js': 'features/priority/priority-calculator.js',
  'priority-calculator-with-worker.js': 'features/priority/priority-calculator-with-worker.js',
  'priority-list-sorting.js': 'features/priority/priority-list-sorting.js',
  'priority-list-utils.js': 'features/priority/priority-list-utils.js',
  'priority-sync-fix.js': 'features/priority/priority-sync-fix.js',
  'priority-worker-wrapper.js': 'features/priority/priority-worker-wrapper.js',
  
  // Schedule files
  'scheduleManager.js': 'features/schedule/scheduleManager.js',
  'timetableAnalyzer.js': 'features/schedule/timetableAnalyzer.js',
  'timetableHandler.js': 'features/schedule/timetableHandler.js',
  'timetableIntegration.js': 'features/schedule/timetableIntegration.js',
  
  // Sleep files
  'sleep-saboteurs-init.js': 'features/sleep/sleep-saboteurs-init.js',
  'sleepScheduleManager.js': 'features/sleep/sleepScheduleManager.js',
  'sleepTimeCalculator.js': 'features/sleep/sleepTimeCalculator.js',
  'energyHologram.js': 'features/sleep/energyHologram.js',
  'energyLevels.js': 'features/sleep/energyLevels.js',
  
  // Study spaces files
  'studySpaceAnalyzer.js': 'features/study-spaces/studySpaceAnalyzer.js',
  'studySpacesFirestore.js': 'features/study-spaces/studySpacesFirestore.js',
  'studySpacesManager.js': 'features/study-spaces/studySpacesManager.js',
  
  // Tasks files
  'currentTaskManager.js': 'features/tasks/currentTaskManager.js',
  'taskAttachments.js': 'features/tasks/taskAttachments.js',
  'taskFilters.js': 'features/tasks/taskFilters.js',
  'taskLinks.js': 'features/tasks/taskLinks.js',
  'task-notes.js': 'features/tasks/task-notes.js',
  'task-notes-injector.js': 'features/tasks/task-notes-injector.js',
  'tasksManager.js': 'features/tasks/tasksManager.js',
  'subtasks.js': 'features/tasks/subtasks.js',
  
  // Workspace files
  'workspace-attachments.js': 'features/workspace/workspace-attachments.js',
  'workspace-core.js': 'features/workspace/workspace-core.js',
  'workspace-document.js': 'features/workspace/workspace-document.js',
  'workspaceFlashcardIntegration.js': 'features/workspace/workspaceFlashcardIntegration.js',
  'workspace-formatting.js': 'features/workspace/workspace-formatting.js',
  'workspace-media.js': 'features/workspace/workspace-media.js',
  'workspace-tables-links.js': 'features/workspace/workspace-tables-links.js',
  'workspace-ui.js': 'features/workspace/workspace-ui.js',
  
  // Integrations files
  'todoistIntegration.js': 'integrations/todoistIntegration.js',
  'googleDriveApi.js': 'integrations/googleDriveApi.js',
  
  // Services files
  'auth.js': 'services/auth.js',
  'firebaseAuth.js': 'services/firebaseAuth.js',
  'firebase-config.js': 'services/firebase-config.js',
  'firebaseConfig.js': 'services/firebaseConfig.js',
  'firebase-init.js': 'services/firebase-init.js',
  'firestore.js': 'services/firestore.js',
  'firestore-global.js': 'services/firestore-global.js',
  'initFirestoreData.js': 'services/initFirestoreData.js',
  'service-worker.js': 'services/service-worker.js',
  'server.js': 'services/server.js',
  
  // UI files
  'add-favicon.js': 'ui/add-favicon.js',
  'fileViewer.js': 'ui/fileViewer.js',
  'sideDrawer.js': 'ui/sideDrawer.js',
  'ui-utilities.js': 'ui/ui-utilities.js',
  'userGuidance.js': 'ui/userGuidance.js',
  'quoteManager.js': 'ui/quoteManager.js',
  'roleModelManager.js': 'ui/roleModelManager.js',
  
  // Utils files
  'grind-speech-synthesis.js': 'utils/grind-speech-synthesis.js',
  'imageAnalysis.js': 'utils/imageAnalysis.js',
  'imageAnalyzer.js': 'utils/imageAnalyzer.js',
  'markdown-converter.js': 'utils/markdown-converter.js',
  'pandoc-fallback.js': 'utils/pandoc-fallback.js',
  'recipeManager.js': 'utils/recipeManager.js',
  'reorganize-scripts.js': 'utils/reorganize-scripts.js',
  'soundManager.js': 'utils/soundManager.js',
  'speech-recognition.js': 'utils/speech-recognition.js',
  'speech-synthesis.js': 'utils/speech-synthesis.js',
  'text-expansion.js': 'utils/text-expansion.js',
  'update-html-files.js': 'utils/update-html-files.js',
  'worker.js': 'utils/worker.js',
  'test-worker.js': 'utils/test-worker.js',
  'test-feedback.js': 'utils/test-feedback.js'
};

// Function to recursively find all JavaScript files
async function findJsFiles(dir) {
  const files = await readdir(dir);
  const jsFiles = [];

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stats = await stat(filePath);

    if (stats.isDirectory()) {
      const subDirFiles = await findJsFiles(filePath);
      jsFiles.push(...subDirFiles);
    } else if (file.endsWith('.js')) {
      jsFiles.push(filePath);
    }
  }

  return jsFiles;
}

// Function to update import statements in a JavaScript file
async function updateJsFile(filePath) {
  console.log(`Processing ${filePath}...`);
  let content = await readFile(filePath, 'utf8');
  let updated = false;

  // Check for import statements
  const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+[^,]+|[^,{}]+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+[^,]+|[^,{}]+))*)?(?:\s*from\s*)?['"]([^'"]+)['"]/g;
  
  let match;
  const matches = [];
  while ((match = importRegex.exec(content)) !== null) {
    matches.push(match);
  }

  // Process matches in reverse order to avoid messing up indices
  for (let i = matches.length - 1; i >= 0; i--) {
    const match = matches[i];
    const importPath = match[1];
    
    // Only process relative imports that don't start with './'
    if (!importPath.startsWith('./') && !importPath.startsWith('../') && !importPath.startsWith('http') && !importPath.includes('/')) {
      const jsFileName = importPath.endsWith('.js') ? importPath : `${importPath}.js`;
      
      if (filePathMap[jsFileName]) {
        const newImportPath = `./${filePathMap[jsFileName]}`;
        const start = match.index + match[0].indexOf(importPath);
        const end = start + importPath.length;
        
        content = content.substring(0, start) + newImportPath + content.substring(end);
        updated = true;
        console.log(`  Updated import: ${importPath} -> ${newImportPath}`);
      }
    }
  }

  // Check for dynamic imports
  const dynamicImportRegex = /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
  
  let dynamicMatch;
  const dynamicMatches = [];
  while ((dynamicMatch = dynamicImportRegex.exec(content)) !== null) {
    dynamicMatches.push(dynamicMatch);
  }

  // Process dynamic import matches in reverse order
  for (let i = dynamicMatches.length - 1; i >= 0; i--) {
    const match = dynamicMatches[i];
    const importPath = match[1];
    
    // Only process relative imports that don't start with './'
    if (!importPath.startsWith('./') && !importPath.startsWith('../') && !importPath.startsWith('http') && !importPath.includes('/')) {
      const jsFileName = importPath.endsWith('.js') ? importPath : `${importPath}.js`;
      
      if (filePathMap[jsFileName]) {
        const newImportPath = `./${filePathMap[jsFileName]}`;
        const start = match.index + match[0].indexOf(importPath);
        const end = start + importPath.length;
        
        content = content.substring(0, start) + newImportPath + content.substring(end);
        updated = true;
        console.log(`  Updated dynamic import: ${importPath} -> ${newImportPath}`);
      }
    }
  }

  if (updated) {
    await writeFile(filePath, content, 'utf8');
    console.log(`  Saved changes to ${filePath}`);
  } else {
    console.log(`  No changes needed for ${filePath}`);
  }
}

// Main function to update all JavaScript files
async function updateAllJsFiles() {
  try {
    const rootDir = path.resolve(__dirname);
    console.log(`Searching for JavaScript files in ${rootDir}...`);
    const jsFiles = await findJsFiles(rootDir);
    console.log(`Found ${jsFiles.length} JavaScript files.`);

    for (const jsFile of jsFiles) {
      await updateJsFile(jsFile);
    }

    console.log('All JavaScript files have been updated successfully!');
  } catch (error) {
    console.error('Error updating JavaScript files:', error);
  }
}

// Run the script
updateAllJsFiles();
