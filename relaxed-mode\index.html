<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Relaxed Mode</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="style.css">
    <!-- Side Drawer Styles -->
    <link rel="stylesheet" href="css/features/sideDrawer.css">

    <!-- Firebase SDK -->




    <!-- Import Firestore functions -->






</head>
<body class="relaxed-mode">
    <button onclick="window.location.href='../grind.html'" class="back-to-grind-btn">
        <i class="bi bi-arrow-left"></i>
        Back to Grind
    </button>

    <!-- Quick Add Task Floating Action Button -->
    <button class="quick-add-fab" id="quickAddBtn"><i class="bi bi-plus-lg"></i></button>

    <!-- Quick Add Task Modal -->
    <div class="quick-add-modal" id="quickAddModal">
        <div class="quick-add-form">
            <div class="quick-add-form-header">
                <span class="quick-add-form-title">Quick Add Activity</span>
                <button class="quick-add-close" id="quickAddClose"><i class="bi bi-x"></i></button>
            </div>
            <input type="text" id="quickTaskTitle" class="form-control" placeholder="Activity Title">
            <div class="form-group">
                <select id="quickPriority" class="form-control">
                    <option value="low">Low Priority</option>
                    <option value="medium">Medium Priority</option>
                    <option value="high">High Priority</option>
                </select>
            </div>
            <div class="quick-add-actions">
                <button id="quickSaveBtn" class="save-btn"><i class="bi bi-check-lg"></i> Save</button>
                <button id="quickCancelBtn" class="cancel-btn"><i class="bi bi-x-lg"></i> Cancel</button>
            </div>
        </div>
    </div>
    <div class="container">
        <header class="relaxed-header">
            <h1>Extracurricular Activities</h1>
            <p class="subtitle">Manage your activities in a relaxed environment</p>
        </header>

        <div class="tasks-container">
            <div class="add-task-section">
                <button class="add-task-btn" onclick="showAddTaskForm()">
                    <i class="bi bi-plus-circle"></i> Add New Activity
                </button>
            </div>

            <div id="taskForm" class="task-form" style="display: none;">
                <input type="text" id="taskTitle" class="form-control" placeholder="Activity Title">
                <textarea id="taskDescription" class="form-control" placeholder="Activity Description"></textarea>
                <div class="form-group">
                    <label for="dueDate">Due Date</label>
                    <input type="date" id="dueDate" class="form-control">
                </div>
                <div class="form-group">
                    <label for="priority">Priority</label>
                    <select id="priority" class="form-control">
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                    </select>
                </div>
                <button onclick="saveTask()" class="save-btn"><i class="bi bi-check-circle"></i> Save Activity</button>
                <button onclick="hideAddTaskForm()" class="cancel-btn"><i class="bi bi-x-circle"></i> Cancel</button>
            </div>

            <div id="tasksList" class="tasks-list">
                <!-- Tasks will be dynamically added here -->
            </div>
        </div>
    </div>








</body>
</html>








</body>
</html>
    <!-- Scripts -->
    <!-- Firebase Initialization -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { firebaseConfig } from '../js/firebaseConfig.js';

        // Initialize Firebase safely
        let app;
        try {
            app = initializeApp(firebaseConfig);
            console.log("Firebase initialized successfully");

            // Set up global variables
            window.db = getFirestore(app);
            window.auth = getAuth(app);
        } catch (e) {
            if (e.code === 'app/duplicate-app') {
                console.log("Firebase already initialized, using existing app");
                try {
                    app = initializeApp();
                    window.db = getFirestore(app);
                    window.auth = getAuth(app);
                } catch(getAppError) {
                    console.error("Could not get existing Firebase app instance.", getAppError);
                }
            } else {
                console.error("Firebase initialization error:", e);
            }
        }
    </script>

    <!-- Authentication Setup -->
    <script type="module">
        import { auth as importedAuth, signInWithGoogle, signOutUser, initializeAuth } from '../js/auth.js';
        window.auth = window.auth || importedAuth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;

        // Initialize authentication
        document.addEventListener('DOMContentLoaded', () => {
            initializeAuth();
        });
    </script>

    <!-- Firestore Data Operations -->
    <script type="module">
        import { saveTasksToFirestore, loadTasksFromFirestore, saveCompletedTaskToFirestore } from '../js/firestore.js';
        import { initializeFirestoreData } from '../js/initFirestoreData.js';

        // Make functions available globally
        window.saveTasksToFirestore = saveTasksToFirestore;
        window.loadTasksFromFirestore = loadTasksFromFirestore;
        window.saveCompletedTaskToFirestore = saveCompletedTaskToFirestore;
        window.initializeFirestoreData = initializeFirestoreData;

        // Initialize data when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (typeof window.initializeFirestoreData === 'function') {
                    window.initializeFirestoreData();
                }
            }, 1500);
        });
    </script>

    <script src="js/common/common.js" defer></script>
    <script type="module" src="js/data/cross-tab-sync.js"></script>
    <script src="js/features/tasks/tasksManager.js" defer></script>
    <script src="js/common/transitionManager.js" defer></script>
    <script src="js/ui/sideDrawer.js"></script>
    <script src="script.js" defer></script>
</body>
</html>