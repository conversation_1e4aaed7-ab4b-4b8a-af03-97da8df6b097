#!/usr/bin/env python3
"""
<PERSON>ript to check if JavaScript and CSS references in HTML files have been properly updated.
This script scans all HTML files in the project, looks for JavaScript and CSS file references,
and verifies if they match the new directory structure.
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple

# Map of old file paths to new file paths
FILE_PATH_MAP = {
    # API files
    'js/api-optimization.js': 'js/api/api-optimization.js',
    'js/api-settings.js': 'js/api/api-settings.js',
    'js/apiSettingsManager.js': 'js/api/apiSettingsManager.js',
    'js/gemini-api.js': 'js/api/gemini-api.js',
    'js/googleGenerativeAI.js': 'js/api/googleGenerativeAI.js',

    # Alarm files
    'js/alarm-data-service.js': 'js/alarms/alarm-data-service.js',
    'js/alarm-handler.js': 'js/alarms/alarm-handler.js',
    'js/alarm-mini-display.js': 'js/alarms/alarm-mini-display.js',
    'js/alarm-service.js': 'js/alarms/alarm-service.js',
    'js/alarm-service-worker.js': 'js/alarms/alarm-service-worker.js',

    # Calendar files
    'js/calendarManager.js': 'js/calendar/calendarManager.js',
    'js/calendar-views.js': 'js/calendar/calendar-views.js',

    # Common files
    'js/common.js': 'js/common/common.js',
    'js/common-header.js': 'js/common/common-header.js',
    'js/inject-header.js': 'js/common/inject-header.js',
    'js/transitionManager.js': 'js/common/transitionManager.js',
    'js/theme-manager.js': 'js/common/theme-manager.js',
    'js/themeManager.js': 'js/common/themeManager.js',
    'js/clock-display.js': 'js/common/clock-display.js',

    # Data files
    'js/cacheManager.js': 'js/data/cacheManager.js',
    'js/cross-tab-sync.js': 'js/data/cross-tab-sync.js',
    'js/data-loader.js': 'js/data/data-loader.js',
    'js/dataStorage.js': 'js/data/dataStorage.js',
    'js/data-sync-integration.js': 'js/data/data-sync-integration.js',
    'js/data-sync-manager.js': 'js/data/data-sync-manager.js',
    'js/indexedDB.js': 'js/data/indexedDB.js',
    'js/storageManager.js': 'js/data/storageManager.js',

    # Academic details files
    'js/semester-management.js': 'js/features/academic-details/semester-management.js',
    'js/subject-management.js': 'js/features/academic-details/subject-management.js',

    # AI files
    'js/ai-latex-conversion.js': 'js/features/ai/ai-latex-conversion.js',
    'js/ai-researcher.js': 'js/features/ai/ai-researcher.js',
    'js/simulation-enhancer.js': 'js/features/ai/simulation-enhancer.js',

    # Flashcards files
    'js/flashcardManager.js': 'js/features/flashcards/flashcardManager.js',
    'js/flashcards.js': 'js/features/flashcards/flashcards.js',
    'js/flashcardTaskIntegration.js': 'js/features/flashcards/flashcardTaskIntegration.js',
    'js/sm2.js': 'js/features/flashcards/sm2.js',

    # Marks tracking files
    'js/marks-tracking.js': 'js/features/marks-tracking/marks-tracking.js',
    'js/subject-marks.js': 'js/features/marks-tracking/subject-marks.js',
    'js/subject-marks-integration.js': 'js/features/marks-tracking/subject-marks-integration.js',
    'js/subject-marks-ui.js': 'js/features/marks-tracking/subject-marks-ui.js',
    'js/weightage-connector.js': 'js/features/marks-tracking/weightage-connector.js',

    # Pomodoro files
    'js/pomodoroGlobal.js': 'js/features/pomodoro/pomodoroGlobal.js',
    'js/pomodoroTimer.js': 'js/features/pomodoro/pomodoroTimer.js',

    # Priority files
    'js/priority-calculator.js': 'js/features/priority/priority-calculator.js',
    'js/priority-calculator-with-worker.js': 'js/features/priority/priority-calculator-with-worker.js',
    'js/priority-list-sorting.js': 'js/features/priority/priority-list-sorting.js',
    'js/priority-list-utils.js': 'js/features/priority/priority-list-utils.js',
    'js/priority-sync-fix.js': 'js/features/priority/priority-sync-fix.js',
    'js/priority-worker-wrapper.js': 'js/features/priority/priority-worker-wrapper.js',

    # Schedule files
    'js/scheduleManager.js': 'js/features/schedule/scheduleManager.js',
    'js/timetableAnalyzer.js': 'js/features/schedule/timetableAnalyzer.js',
    'js/timetableHandler.js': 'js/features/schedule/timetableHandler.js',
    'js/timetableIntegration.js': 'js/features/schedule/timetableIntegration.js',

    # Sleep files
    'js/sleep-saboteurs-init.js': 'js/features/sleep/sleep-saboteurs-init.js',
    'js/sleepScheduleManager.js': 'js/features/sleep/sleepScheduleManager.js',
    'js/sleepTimeCalculator.js': 'js/features/sleep/sleepTimeCalculator.js',
    'js/energyHologram.js': 'js/features/sleep/energyHologram.js',
    'js/energyLevels.js': 'js/features/sleep/energyLevels.js',

    # Study spaces files
    'js/studySpaceAnalyzer.js': 'js/features/study-spaces/studySpaceAnalyzer.js',
    'js/studySpacesFirestore.js': 'js/features/study-spaces/studySpacesFirestore.js',
    'js/studySpacesManager.js': 'js/features/study-spaces/studySpacesManager.js',

    # Tasks files
    'js/currentTaskManager.js': 'js/features/tasks/currentTaskManager.js',
    'js/taskAttachments.js': 'js/features/tasks/taskAttachments.js',
    'js/taskFilters.js': 'js/features/tasks/taskFilters.js',
    'js/taskLinks.js': 'js/features/tasks/taskLinks.js',
    'js/task-notes.js': 'js/features/tasks/task-notes.js',
    'js/task-notes-injector.js': 'js/features/tasks/task-notes-injector.js',
    'js/tasksManager.js': 'js/features/tasks/tasksManager.js',
    'js/subtasks.js': 'js/features/tasks/subtasks.js',

    # Workspace files
    'js/workspace-attachments.js': 'js/features/workspace/workspace-attachments.js',
    'js/workspace-core.js': 'js/features/workspace/workspace-core.js',
    'js/workspace-document.js': 'js/features/workspace/workspace-document.js',
    'js/workspaceFlashcardIntegration.js': 'js/features/workspace/workspaceFlashcardIntegration.js',
    'js/workspace-formatting.js': 'js/features/workspace/workspace-formatting.js',
    'js/workspace-media.js': 'js/features/workspace/workspace-media.js',
    'js/workspace-tables-links.js': 'js/features/workspace/workspace-tables-links.js',
    'js/workspace-ui.js': 'js/features/workspace/workspace-ui.js',

    # Integrations files
    'js/todoistIntegration.js': 'js/integrations/todoistIntegration.js',
    'js/googleDriveApi.js': 'js/integrations/googleDriveApi.js',

    # Services files
    'js/auth.js': 'js/services/auth.js',
    'js/firebaseAuth.js': 'js/services/firebaseAuth.js',
    'js/firebase-config.js': 'js/services/firebase-config.js',
    'js/firebaseConfig.js': 'js/services/firebaseConfig.js',
    'js/firebase-init.js': 'js/services/firebase-init.js',
    'js/firestore.js': 'js/services/firestore.js',
    'js/firestore-global.js': 'js/services/firestore-global.js',
    'js/initFirestoreData.js': 'js/services/initFirestoreData.js',
    'js/service-worker.js': 'js/services/service-worker.js',
    'js/server.js': 'js/services/server.js',

    # UI files
    'js/add-favicon.js': 'js/ui/add-favicon.js',
    'js/fileViewer.js': 'js/ui/fileViewer.js',
    'js/sideDrawer.js': 'js/ui/sideDrawer.js',
    'js/ui-utilities.js': 'js/ui/ui-utilities.js',
    'js/userGuidance.js': 'js/ui/userGuidance.js',
    'js/quoteManager.js': 'js/ui/quoteManager.js',
    'js/roleModelManager.js': 'js/ui/roleModelManager.js',

    # Utils files
    'js/grind-speech-synthesis.js': 'js/utils/grind-speech-synthesis.js',
    'js/imageAnalysis.js': 'js/utils/imageAnalysis.js',
    'js/imageAnalyzer.js': 'js/utils/imageAnalyzer.js',
    'js/markdown-converter.js': 'js/utils/markdown-converter.js',
    'js/pandoc-fallback.js': 'js/utils/pandoc-fallback.js',
    'js/recipeManager.js': 'js/utils/recipeManager.js',
    'js/reorganize-scripts.js': 'js/utils/reorganize-scripts.js',
    'js/soundManager.js': 'js/utils/soundManager.js',
    'js/speech-recognition.js': 'js/utils/speech-recognition.js',
    'js/speech-synthesis.js': 'js/utils/speech-synthesis.js',
    'js/text-expansion.js': 'js/utils/text-expansion.js',
    'js/update-html-files.js': 'js/utils/update-html-files.js',
    'js/worker.js': 'js/utils/worker.js',
    'js/test-worker.js': 'js/utils/test-worker.js',
    'js/test-feedback.js': 'js/utils/test-feedback.js',

    # CSS Core files
    'css/main.css': 'css/core/main.css',
    'css/index.css': 'css/core/index.css',

    # CSS Components files
    'css/academic-details.css': 'css/components/academic-details.css',
    'css/subject-marks.css': 'css/components/subject-marks.css',

    # CSS Calendar-Tasks files
    'css/calendar.css': 'css/calendar-tasks/calendar.css',
    'css/daily-calendar.css': 'css/calendar-tasks/daily-calendar.css',
    'css/tasks.css': 'css/calendar-tasks/tasks.css',
    'css/task-display.css': 'css/calendar-tasks/task-display.css',
    'css/task-notes.css': 'css/calendar-tasks/task-notes.css',
    'css/taskLinks.css': 'css/calendar-tasks/taskLinks.css',

    # CSS Utilities files
    'css/extracted.css': 'css/utilities/extracted.css',
    'css/compact-style.css': 'css/utilities/compact-style.css',

    # CSS Features files
    'css/ai-search-response.css': 'css/features/ai-search-response.css',
    'css/notification.css': 'css/features/notification.css',
    'css/settings.css': 'css/features/settings.css',
    'css/sideDrawer.css': 'css/features/sideDrawer.css',
    'css/workspace.css': 'css/features/workspace.css',
    'css/alarm-service.css': 'css/features/alarm-service.css',

    # CSS Study-Tools files
    'css/flashcards.css': 'css/study-tools/flashcards.css',
    'css/grind.css': 'css/study-tools/grind.css',
    'css/sleep-saboteurs.css': 'css/study-tools/sleep-saboteurs.css',
    'css/study-spaces.css': 'css/study-tools/study-spaces.css',
    'css/text-expansion.css': 'css/study-tools/text-expansion.css',

    # CSS Calculators files
    'css/priority-calculator.css': 'css/calculators/priority-calculator.css',
    'css/priority-list.css': 'css/calculators/priority-list.css',
    'css/simulation-enhancer.css': 'css/calculators/simulation-enhancer.css',
    'css/test-feedback.css': 'css/calculators/test-feedback.css'
}

def find_html_files(directory: str) -> List[str]:
    """Find all HTML files in the given directory and its subdirectories."""
    html_files = []
    for root, _, files in os.walk(directory):
        if "node_modules" in root:
            continue
        for file in files:
            if file.endswith(".html"):
                html_files.append(os.path.join(root, file))
    return html_files

def check_html_file(file_path: str) -> List[Tuple[str, str, str]]:
    """
    Check if JavaScript and CSS references in an HTML file have been properly updated.
    Returns a list of tuples (file_path, old_path, expected_new_path) for references that need to be updated.
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    issues = []

    # Find all script tags with src attribute
    script_tags = re.findall(r'<script\s+[^>]*src\s*=\s*["\']([^"\']+)["\'][^>]*>', content)

    for src in script_tags:
        # Skip external scripts and scripts that don't reference js files
        if src.startswith(('http:', 'https:', '//')) or not src.endswith('.js'):
            continue

        # Check if this is an old path that should have been updated
        for old_path, new_path in FILE_PATH_MAP.items():
            # Handle both absolute and relative paths
            if src == old_path or src.endswith('/' + old_path):
                issues.append((file_path, src, new_path))
                break

    # Find all link tags with href attribute (CSS files)
    link_tags = re.findall(r'<link\s+[^>]*href\s*=\s*["\']([^"\']+)["\'][^>]*>', content)

    for href in link_tags:
        # Skip external stylesheets and links that don't reference css files
        if href.startswith(('http:', 'https:', '//')) or not href.endswith('.css'):
            continue

        # Check if this is an old path that should have been updated
        for old_path, new_path in FILE_PATH_MAP.items():
            # Handle both absolute and relative paths
            if href == old_path or href.endswith('/' + old_path):
                issues.append((file_path, href, new_path))
                break

    return issues

def fix_html_file(file_path: str, issues: List[Tuple[str, str, str]]) -> None:
    """Fix JavaScript and CSS references in an HTML file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    for _, old_path, new_path in issues:
        # Replace the old path with the new path in script tags
        content = content.replace(f'src="{old_path}"', f'src="{new_path}"')
        content = content.replace(f"src='{old_path}'", f"src='{new_path}'")

        # Replace the old path with the new path in link tags
        content = content.replace(f'href="{old_path}"', f'href="{new_path}"')
        content = content.replace(f"href='{old_path}'", f"href='{new_path}'")

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def main():
    # Get the project root directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)

    print(f"Checking HTML files in {project_root} for JS and CSS references...")

    # Find all HTML files
    html_files = find_html_files(project_root)
    print(f"Found {len(html_files)} HTML files.")

    # Check each HTML file
    all_issues = []
    for html_file in html_files:
        issues = check_html_file(html_file)
        if issues:
            all_issues.extend(issues)
            rel_path = os.path.relpath(html_file, project_root)
            print(f"\nIssues found in {rel_path}:")
            for _, old_path, new_path in issues:
                print(f"  {old_path} -> {new_path}")

    # Summary
    if all_issues:
        print(f"\nFound {len(all_issues)} issues in {len(set(issue[0] for issue in all_issues))} files.")

        # Ask if user wants to fix the issues
        fix_choice = input("\nDo you want to automatically fix these issues? (y/n): ")
        if fix_choice.lower() == 'y':
            # Group issues by file
            issues_by_file = {}
            for issue in all_issues:
                file_path = issue[0]
                if file_path not in issues_by_file:
                    issues_by_file[file_path] = []
                issues_by_file[file_path].append(issue)

            # Fix each file
            for file_path, file_issues in issues_by_file.items():
                rel_path = os.path.relpath(file_path, project_root)
                print(f"Fixing {rel_path}...")
                fix_html_file(file_path, file_issues)

            print("\nAll issues have been fixed!")
        else:
            print("\nNo changes were made.")
    else:
        print("\nNo issues found! All JavaScript references appear to be updated correctly.")

if __name__ == "__main__":
    main()
