# Task Groups

- **Group A:** Tasks 1 & 2 (Overall Structure Review & CSS Reference Checks)
- **Group B:** Tasks 3 & 4 (JS Reference Checks & Asset Checks)  
- **Group C:** Tasks 5 & 6 (Final Audit & Documentation)

---

# Codebase Verification Tasks

## 📁 Task 1: Overall Structure Review
- [ ] **Subtask 1.1:** List all HTML files in project root
  - [ ] 1.1.1: index.html (redirect page)
  - [ ] 1.1.2: landing.html (main landing page)
  - [ ] 1.1.3: grind.html (main dashboard)
  - [ ] 1.1.4: daily-calendar.html
  - [ ] 1.1.5: academic-details.html
  - [ ] 1.1.6: extracted.html
  - [ ] 1.1.7: flashcards.html
  - [ ] 1.1.8: instant-test-feedback.html
  - [ ] 1.1.9: priority-calculator.html
  - [ ] 1.1.10: priority-list.html
  - [ ] 1.1.11: settings.html
  - [ ] 1.1.12: sleep-saboteurs.html
  - [ ] 1.1.13: study-spaces.html
  - [ ] 1.1.14: subject-marks.html
  - [ ] 1.1.15: tasks.html
  - [ ] 1.1.16: workspace.html
  - [ ] 1.1.17: 404.html
  - [ ] 1.1.18: relaxed-mode/index.html

- [ ] **Subtask 1.2:** Verify directory structure
  - [ ] 1.2.1: /assets directory exists
  - [ ] 1.2.2: /css directory exists
  - [ ] 1.2.3: /js directory exists
  - [ ] 1.2.4: /relaxed-mode directory exists
  - [ ] 1.2.5: /uploads directory exists

## 🔗 Task 2: CSS Reference Checks
- [ ] **Subtask 2.1:** Verify `<link>` tags in index.html
  - [ ] 2.1.1: css/features/alarm-service.css → exists
  - [ ] 2.1.2: assets/images/gpace-logo-white.png (favicon) → exists

- [ ] **Subtask 2.2:** Verify `<link>` tags in landing.html
  - [ ] 2.2.1: assets/images/gpace-logo-white.png (favicon) → exists
  - [ ] 2.2.2: styles/main.css → check if exists (potential issue)
  - [ ] 2.2.3: css/features/sideDrawer.css → exists

- [ ] **Subtask 2.3:** Verify `<link>` tags in grind.html
  - [ ] 2.3.1: css/features/ai-search-response.css → exists
  - [ ] 2.3.2: css/search-modal.css → check if exists
  - [ ] 2.3.3: css/features/sideDrawer.css → exists
  - [ ] 2.3.4: css/calculators/simulation-enhancer.css → exists
  - [ ] 2.3.5: css/calendar-tasks/task-display.css → exists
  - [ ] 2.3.6: css/calendar-tasks/task-notes.css → exists
  - [ ] 2.3.7: css/calendar-tasks/taskLinks.css → exists
  - [ ] 2.3.8: css/study-tools/text-expansion.css → exists
  - [ ] 2.3.9: grind.css → check if exists in root
  - [ ] 2.3.10: main.css → check if exists in root

- [ ] **Subtask 2.4:** Verify `<link>` tags in daily-calendar.html
  - [ ] 2.4.1: main.css → check if exists in root
  - [ ] 2.4.2: css/features/sideDrawer.css → exists
  - [ ] 2.4.3: css/calendar-tasks/daily-calendar.css → exists
  - [ ] 2.4.4: css/calendar-tasks/task-display.css → exists

- [ ] **Subtask 2.5:** Check remaining HTML files for CSS references
  - [ ] 2.5.1: academic-details.html CSS references
  - [ ] 2.5.2: extracted.html CSS references
  - [ ] 2.5.3: flashcards.html CSS references
  - [ ] 2.5.4: instant-test-feedback.html CSS references
  - [ ] 2.5.5: priority-calculator.html CSS references
  - [ ] 2.5.6: priority-list.html CSS references
  - [ ] 2.5.7: settings.html CSS references
  - [ ] 2.5.8: sleep-saboteurs.html CSS references
  - [ ] 2.5.9: study-spaces.html CSS references
  - [ ] 2.5.10: subject-marks.html CSS references
  - [ ] 2.5.11: tasks.html CSS references
  - [ ] 2.5.12: workspace.html CSS references
  - [ ] 2.5.13: relaxed-mode/index.html CSS references

## 📝 Task 3: JS Reference Checks
- [ ] **Subtask 3.1:** Verify `<script>` tags in index.html
  - [ ] 3.1.1: js/data/cacheManager.js → exists
  - [ ] 3.1.2: js/data/cross-tab-sync.js → exists
  - [ ] 3.1.3: js/alarms/alarm-service.js → exists
  - [ ] 3.1.4: js/alarms/alarm-mini-display.js → exists
  - [ ] 3.1.5: js/common/inject-header.js → exists

- [ ] **Subtask 3.2:** Verify `<script>` tags in landing.html
  - [ ] 3.2.1: js/theme-toggle.js → check if exists
  - [ ] 3.2.2: js/data/cross-tab-sync.js → exists
  - [ ] 3.2.3: js/common/inject-header.js → exists

- [ ] **Subtask 3.3:** Verify `<script>` tags in grind.html (first 50 scripts)
  - [ ] 3.3.1: Check all script references in grind.html
  - [ ] 3.3.2: Verify module imports and dependencies

- [ ] **Subtask 3.4:** Verify `<script>` tags in daily-calendar.html
  - [ ] 3.4.1: js/ui/sideDrawer.js → exists
  - [ ] 3.4.2: js/services/firebaseAuth.js → exists
  - [ ] 3.4.3: js/features/sleep/sleepScheduleManager.js → check path
  - [ ] 3.4.4: js/calendar-views.js → check if exists in root
  - [ ] 3.4.5: js/calendar/calendarManager.js → exists
  - [ ] 3.4.6: js/features/schedule/timetableIntegration.js → check path
  - [ ] 3.4.7: js/features/tasks/currentTaskManager.js → check path

- [ ] **Subtask 3.5:** Check remaining HTML files for JS references
  - [ ] 3.5.1: academic-details.html JS references
  - [ ] 3.5.2: extracted.html JS references
  - [ ] 3.5.3: flashcards.html JS references
  - [ ] 3.5.4: instant-test-feedback.html JS references
  - [ ] 3.5.5: priority-calculator.html JS references
  - [ ] 3.5.6: priority-list.html JS references
  - [ ] 3.5.7: settings.html JS references
  - [ ] 3.5.8: sleep-saboteurs.html JS references
  - [ ] 3.5.9: study-spaces.html JS references
  - [ ] 3.5.10: subject-marks.html JS references
  - [ ] 3.5.11: tasks.html JS references
  - [ ] 3.5.12: workspace.html JS references
  - [ ] 3.5.13: relaxed-mode/index.html JS references

## 🖼️ Task 4: Asset Checks
- [ ] **Subtask 4.1:** Verify `<img>` tags and asset references
  - [ ] 4.1.1: assets/images/gpace-logo-white.png → exists (used in multiple files)
  - [ ] 4.1.2: Check for any other image references in HTML files

- [ ] **Subtask 4.2:** Verify audio asset references
  - [ ] 4.2.1: assets/pop.mp3 → exists
  - [ ] 4.2.2: assets/sounds/notification.mp3 → exists
  - [ ] 4.2.3: assets/sounds/pop.mp3 → exists
  - [ ] 4.2.4: assets/alarm-sounds/alarm1.mp3 → exists
  - [ ] 4.2.5: assets/alarm-sounds/alarm2.mp3 → exists
  - [ ] 4.2.6: assets/alarm-sounds/alarm3.mp3 → exists

- [ ] **Subtask 4.3:** Check for missing asset directories
  - [ ] 4.3.1: Verify all referenced asset paths exist
  - [ ] 4.3.2: Check for broken relative paths

## ✅ Task 5: Final Audit
- [ ] **Subtask 5.1:** Browser & Console Check
  - [ ] 5.1.1: Load index.html → watch for 404s/errors
  - [ ] 5.1.2: Load landing.html → watch for 404s/errors
  - [ ] 5.1.3: Load grind.html → watch for 404s/errors
  - [ ] 5.1.4: Load daily-calendar.html → watch for 404s/errors
  - [ ] 5.1.5: Test navigation between pages

- [ ] **Subtask 5.2:** Identify and document issues
  - [ ] 5.2.1: List all missing files
  - [ ] 5.2.2: List all broken references
  - [ ] 5.2.3: Note typos/incorrect paths
  - [ ] 5.2.4: Document CDN dependencies

## 📋 Task 6: Documentation & Recommendations
- [ ] **Subtask 6.1:** Create issue summary
  - [ ] 6.1.1: High priority fixes (broken core functionality)
  - [ ] 6.1.2: Medium priority fixes (missing assets)
  - [ ] 6.1.3: Low priority fixes (optimization opportunities)

- [ ] **Subtask 6.2:** Provide recommendations
  - [ ] 6.2.1: File organization improvements
  - [ ] 6.2.2: Asset optimization suggestions
  - [ ] 6.2.3: Dependency management recommendations

---

## 🔍 Known Issues to Investigate
- [ ] **Potential Issues Identified:**
  - [ ] styles/main.css referenced in landing.html (should be main.css?)
  - [ ] css/search-modal.css referenced in grind.html (verify exists)
  - [ ] grind.css and main.css in root directory (verify exists)
  - [ ] js/theme-toggle.js referenced in landing.html (verify exists)
  - [ ] js/calendar-views.js referenced in daily-calendar.html (verify path)
  - [ ] Various js/features/ subdirectory paths need verification

## 📊 Progress Tracking
- **Group A Progress:** 0/2 tasks completed
- **Group B Progress:** 0/2 tasks completed  
- **Group C Progress:** 0/2 tasks completed
- **Overall Progress:** 0/6 tasks completed (0%)
