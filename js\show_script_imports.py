#!/usr/bin/env python3
"""
<PERSON>ript to display all script tags imported in an HTML file.
This script allows you to select an HTML file and shows all the JavaScript files it imports.
"""

import os
import re
import sys
from pathlib import Path
import argparse
from typing import List, Dict, Tuple

def find_html_files(directory: str) -> List[str]:
    """Find all HTML files in the given directory and its subdirectories."""
    html_files = []
    for root, _, files in os.walk(directory):
        if "node_modules" in root:
            continue
        for file in files:
            if file.endswith(".html"):
                html_files.append(os.path.join(root, file))
    return html_files

def extract_script_tags(file_path: str) -> List[Dict[str, str]]:
    """
    Extract all script tags from an HTML file.
    Returns a list of dictionaries with information about each script tag.
    """
    with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
        content = f.read()
    
    script_tags = []
    
    # Find all script tags
    pattern = r'<script\s+([^>]*)>(.*?)</script>|<script\s+([^>]*)>'
    matches = re.finditer(pattern, content, re.DOTALL)
    
    for match in matches:
        attrs = match.group(1) or match.group(3) or ""
        
        # Extract src attribute if present
        src_match = re.search(r'src\s*=\s*["\']([^"\']+)["\']', attrs)
        src = src_match.group(1) if src_match else None
        
        # Extract type attribute if present
        type_match = re.search(r'type\s*=\s*["\']([^"\']+)["\']', attrs)
        script_type = type_match.group(1) if type_match else "text/javascript"  # Default type
        
        # Extract other attributes
        other_attrs = {}
        for attr_match in re.finditer(r'(\w+)\s*=\s*["\']([^"\']+)["\']', attrs):
            attr_name = attr_match.group(1)
            if attr_name not in ['src', 'type']:
                other_attrs[attr_name] = attr_match.group(2)
        
        # Extract inline content if present
        inline_content = match.group(2) if match.group(2) else None
        
        script_tags.append({
            'src': src,
            'type': script_type,
            'attrs': other_attrs,
            'has_inline_content': inline_content is not None and inline_content.strip() != ""
        })
    
    return script_tags

def display_script_tags(file_path: str, script_tags: List[Dict[str, str]]) -> None:
    """Display information about script tags in a formatted way."""
    print(f"\nScript tags in {file_path}:")
    print("-" * 80)
    
    if not script_tags:
        print("No script tags found.")
        return
    
    for i, script in enumerate(script_tags, 1):
        print(f"Script #{i}:")
        if script['src']:
            print(f"  Source: {script['src']}")
        else:
            print("  Source: [Inline script]")
        
        print(f"  Type: {script['type']}")
        
        if script['attrs']:
            print("  Other attributes:")
            for name, value in script['attrs'].items():
                print(f"    {name}={value}")
        
        if script.get('has_inline_content'):
            print("  Contains inline JavaScript code")
        
        print()

def select_html_file(html_files: List[str], project_root: str) -> str:
    """Allow the user to select an HTML file from a list."""
    if not html_files:
        print("No HTML files found in the project.")
        sys.exit(1)
    
    # Sort files by name for easier selection
    html_files.sort()
    
    print("\nAvailable HTML files:")
    for i, file_path in enumerate(html_files, 1):
        rel_path = os.path.relpath(file_path, project_root)
        print(f"{i}. {rel_path}")
    
    while True:
        try:
            choice = input("\nEnter the number of the file to analyze (or 'q' to quit): ")
            if choice.lower() == 'q':
                sys.exit(0)
            
            index = int(choice) - 1
            if 0 <= index < len(html_files):
                return html_files[index]
            else:
                print(f"Please enter a number between 1 and {len(html_files)}.")
        except ValueError:
            print("Please enter a valid number.")

def main():
    parser = argparse.ArgumentParser(description='Display script tags in an HTML file.')
    parser.add_argument('file', nargs='?', help='Path to the HTML file to analyze')
    args = parser.parse_args()
    
    # Get the project root directory (parent of js directory)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    
    if args.file:
        # Use the provided file path
        file_path = args.file
        if not os.path.isabs(file_path):
            file_path = os.path.join(project_root, file_path)
        
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found.")
            sys.exit(1)
        
        if not file_path.endswith('.html'):
            print(f"Error: File '{file_path}' is not an HTML file.")
            sys.exit(1)
    else:
        # Find all HTML files and let the user select one
        print(f"Searching for HTML files in {project_root}...")
        html_files = find_html_files(project_root)
        print(f"Found {len(html_files)} HTML files.")
        
        file_path = select_html_file(html_files, project_root)
    
    # Extract and display script tags
    script_tags = extract_script_tags(file_path)
    display_script_tags(os.path.relpath(file_path, project_root), script_tags)
    
    # Offer to analyze another file
    while True:
        choice = input("\nAnalyze another file? (y/n): ")
        if choice.lower() == 'y':
            html_files = find_html_files(project_root)
            file_path = select_html_file(html_files, project_root)
            script_tags = extract_script_tags(file_path)
            display_script_tags(os.path.relpath(file_path, project_root), script_tags)
        else:
            break

if __name__ == "__main__":
    main()
