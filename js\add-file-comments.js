/**
 * <PERSON><PERSON><PERSON> to add comments to JavaScript files after restructuring
 * This script will add a brief comment at the top of each moved file
 * explaining its purpose and context
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// Map of file paths to comments
const fileComments = {
  // API files
  'api/api-optimization.js': 'API optimization utilities for improving API call efficiency and performance.',
  'api/api-settings.js': 'Configuration settings for API endpoints and authentication.',
  'api/apiSettingsManager.js': 'Manager for handling API settings and configurations.',
  'api/gemini-api.js': 'Integration with Google\'s Gemini AI API for AI-powered features.',
  'api/googleGenerativeAI.js': 'Wrapper for Google\'s Generative AI services.',
  
  // Alarm files
  'alarms/alarm-data-service.js': 'Data service for managing alarm data and persistence.',
  'alarms/alarm-handler.js': 'Handler for alarm events and notifications.',
  'alarms/alarm-mini-display.js': 'Compact UI component for displaying alarms.',
  'alarms/alarm-service.js': 'Core service for alarm functionality and scheduling.',
  'alarms/alarm-service-worker.js': 'Background service worker for alarm notifications when the app is closed.',
  
  // Calendar files
  'calendar/calendarManager.js': 'Manager for calendar events, views, and interactions.',
  'calendar/calendar-views.js': 'UI components and views for calendar display.',
  
  // Common files
  'common/common.js': 'Shared utility functions and constants used across the application.',
  'common/common-header.js': 'Common header component used across multiple pages.',
  'common/inject-header.js': 'Script to inject the common header into pages.',
  'common/transitionManager.js': 'Manager for handling page transitions and animations.',
  'common/theme-manager.js': 'Manager for application themes and appearance settings.',
  'common/themeManager.js': 'Alternative theme manager implementation.',
  'common/clock-display.js': 'Clock display component for showing current time.',
  
  // Data files
  'data/cacheManager.js': 'Manager for caching data to improve performance.',
  'data/cross-tab-sync.js': 'Synchronization mechanism for data across browser tabs.',
  'data/data-loader.js': 'Utility for loading data from various sources.',
  'data/dataStorage.js': 'Storage utilities for persisting application data.',
  'data/data-sync-integration.js': 'Integration with data synchronization services.',
  'data/data-sync-manager.js': 'Manager for synchronizing data across devices and services.',
  'data/indexedDB.js': 'Wrapper for IndexedDB operations for local data storage.',
  'data/storageManager.js': 'Manager for various storage mechanisms (local, session, etc.).',
  
  // Academic details files
  'features/academic-details/semester-management.js': 'Management of academic semesters, terms, and periods.',
  'features/academic-details/subject-management.js': 'Management of academic subjects and courses.',
  
  // AI files
  'features/ai/ai-latex-conversion.js': 'AI-powered conversion of text to LaTeX format.',
  'features/ai/ai-researcher.js': 'AI-powered research assistant for academic tasks.',
  'features/ai/simulation-enhancer.js': 'AI enhancement for educational simulations.',
  
  // Flashcards files
  'features/flashcards/flashcardManager.js': 'Manager for flashcard creation, organization, and study.',
  'features/flashcards/flashcards.js': 'Core flashcard functionality and UI components.',
  'features/flashcards/flashcardTaskIntegration.js': 'Integration between flashcards and task management.',
  'features/flashcards/sm2.js': 'Implementation of the SuperMemo 2 (SM2) spaced repetition algorithm.',
  
  // Marks tracking files
  'features/marks-tracking/marks-tracking.js': 'Tracking and analysis of academic marks and grades.',
  'features/marks-tracking/subject-marks.js': 'Management of marks for specific subjects.',
  'features/marks-tracking/subject-marks-integration.js': 'Integration of subject marks with other features.',
  'features/marks-tracking/subject-marks-ui.js': 'UI components for displaying and editing subject marks.',
  'features/marks-tracking/weightage-connector.js': 'Calculation of weighted grades and marks.',
  
  // Pomodoro files
  'features/pomodoro/pomodoroGlobal.js': 'Global state and settings for the Pomodoro timer.',
  'features/pomodoro/pomodoroTimer.js': 'Pomodoro timer implementation for focused work sessions.',
  
  // Priority files
  'features/priority/priority-calculator.js': 'Calculator for task priorities based on various factors.',
  'features/priority/priority-calculator-with-worker.js': 'Web worker implementation of priority calculation for performance.',
  'features/priority/priority-list-sorting.js': 'Sorting algorithms for priority-based task lists.',
  'features/priority/priority-list-utils.js': 'Utility functions for priority lists.',
  'features/priority/priority-sync-fix.js': 'Fixes for priority synchronization issues.',
  'features/priority/priority-worker-wrapper.js': 'Wrapper for the priority calculation web worker.',
  
  // Schedule files
  'features/schedule/scheduleManager.js': 'Manager for scheduling and timetable functionality.',
  'features/schedule/timetableAnalyzer.js': 'Analysis tools for optimizing timetables and schedules.',
  'features/schedule/timetableHandler.js': 'Handler for timetable events and interactions.',
  'features/schedule/timetableIntegration.js': 'Integration of timetables with other features.',
  
  // Sleep files
  'features/sleep/sleep-saboteurs-init.js': 'Initialization for the sleep saboteurs feature.',
  'features/sleep/sleepScheduleManager.js': 'Manager for sleep schedules and recommendations.',
  'features/sleep/sleepTimeCalculator.js': 'Calculator for optimal sleep times and durations.',
  'features/sleep/energyHologram.js': 'Visual representation of energy levels throughout the day.',
  'features/sleep/energyLevels.js': 'Tracking and management of energy levels based on sleep.',
  
  // Study spaces files
  'features/study-spaces/studySpaceAnalyzer.js': 'Analysis of study spaces for productivity optimization.',
  'features/study-spaces/studySpacesFirestore.js': 'Firestore integration for study spaces data.',
  'features/study-spaces/studySpacesManager.js': 'Manager for study spaces and environments.',
  
  // Tasks files
  'features/tasks/currentTaskManager.js': 'Manager for the currently active task.',
  'features/tasks/taskAttachments.js': 'Handling of attachments for tasks.',
  'features/tasks/taskFilters.js': 'Filtering mechanisms for task lists.',
  'features/tasks/taskLinks.js': 'Management of links between related tasks.',
  'features/tasks/task-notes.js': 'Notes and annotations for tasks.',
  'features/tasks/task-notes-injector.js': 'Injection of task notes into the UI.',
  'features/tasks/tasksManager.js': 'Core manager for task creation, editing, and organization.',
  'features/tasks/subtasks.js': 'Management of subtasks within parent tasks.',
  
  // Workspace files
  'features/workspace/workspace-attachments.js': 'Attachment handling for workspace documents.',
  'features/workspace/workspace-core.js': 'Core functionality for the workspace feature.',
  'features/workspace/workspace-document.js': 'Document management within workspaces.',
  'features/workspace/workspaceFlashcardIntegration.js': 'Integration between workspaces and flashcards.',
  'features/workspace/workspace-formatting.js': 'Text and content formatting in workspaces.',
  'features/workspace/workspace-media.js': 'Media handling in workspace documents.',
  'features/workspace/workspace-tables-links.js': 'Tables and links functionality in workspaces.',
  'features/workspace/workspace-ui.js': 'UI components for the workspace feature.',
  
  // Integrations files
  'integrations/todoistIntegration.js': 'Integration with Todoist for task synchronization.',
  'integrations/googleDriveApi.js': 'Integration with Google Drive for file storage and sharing.',
  
  // Services files
  'services/auth.js': 'Authentication service for user login and session management.',
  'services/firebaseAuth.js': 'Firebase-specific authentication implementation.',
  'services/firebase-config.js': 'Configuration for Firebase services.',
  'services/firebaseConfig.js': 'Alternative Firebase configuration.',
  'services/firebase-init.js': 'Initialization of Firebase services.',
  'services/firestore.js': 'Firestore database service for cloud data storage.',
  'services/firestore-global.js': 'Global Firestore instance and utilities.',
  'services/initFirestoreData.js': 'Initialization of Firestore data structures.',
  'services/service-worker.js': 'Service worker for offline functionality and background processing.',
  'services/server.js': 'Server-side functionality for the application.',
  
  // UI files
  'ui/add-favicon.js': 'Utility to add favicons to the application.',
  'ui/fileViewer.js': 'UI component for viewing various file types.',
  'ui/sideDrawer.js': 'Side drawer/navigation component.',
  'ui/ui-utilities.js': 'General UI utility functions.',
  'ui/userGuidance.js': 'User guidance and tutorial components.',
  'ui/quoteManager.js': 'Manager for motivational quotes display.',
  'ui/roleModelManager.js': 'Manager for role model profiles and inspiration.',
  
  // Utils files
  'utils/grind-speech-synthesis.js': 'Speech synthesis for the grind mode feature.',
  'utils/imageAnalysis.js': 'Image analysis and processing utilities.',
  'utils/imageAnalyzer.js': 'Analysis of images for content and metadata.',
  'utils/markdown-converter.js': 'Conversion between Markdown and other formats.',
  'utils/pandoc-fallback.js': 'Fallback for Pandoc document conversion.',
  'utils/recipeManager.js': 'Manager for study and productivity recipes/techniques.',
  'utils/reorganize-scripts.js': 'Utilities for reorganizing scripts and code.',
  'utils/soundManager.js': 'Manager for sound effects and audio feedback.',
  'utils/speech-recognition.js': 'Speech recognition for voice commands.',
  'utils/speech-synthesis.js': 'Text-to-speech functionality.',
  'utils/text-expansion.js': 'Text expansion and shorthand utilities.',
  'utils/update-html-files.js': 'Utilities for updating HTML files.',
  'utils/worker.js': 'Web worker implementation for background processing.',
  'utils/test-worker.js': 'Test implementation of web workers.',
  'utils/test-feedback.js': 'Feedback mechanisms for tests and assessments.'
};

// Function to recursively find all JavaScript files
async function findJsFiles(dir) {
  const files = await readdir(dir);
  const jsFiles = [];

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stats = await stat(filePath);

    if (stats.isDirectory()) {
      const subDirFiles = await findJsFiles(filePath);
      jsFiles.push(...subDirFiles);
    } else if (file.endsWith('.js')) {
      jsFiles.push(filePath);
    }
  }

  return jsFiles;
}

// Function to add a comment to a JavaScript file
async function addCommentToFile(filePath) {
  const relativePath = path.relative(__dirname, filePath).replace(/\\/g, '/');
  
  if (!fileComments[relativePath]) {
    console.log(`No comment defined for ${relativePath}, skipping...`);
    return;
  }
  
  console.log(`Processing ${filePath}...`);
  let content = await readFile(filePath, 'utf8');
  
  // Check if the file already has a comment block at the top
  if (content.trimStart().startsWith('/**')) {
    console.log(`  File already has a comment block, skipping...`);
    return;
  }
  
  // Add the comment block
  const comment = `/**
 * ${fileComments[relativePath]}
 */\n\n`;
  
  content = comment + content;
  
  await writeFile(filePath, content, 'utf8');
  console.log(`  Added comment to ${filePath}`);
}

// Main function to add comments to all JavaScript files
async function addCommentsToAllFiles() {
  try {
    const rootDir = path.resolve(__dirname);
    console.log(`Searching for JavaScript files in ${rootDir}...`);
    const jsFiles = await findJsFiles(rootDir);
    console.log(`Found ${jsFiles.length} JavaScript files.`);

    for (const jsFile of jsFiles) {
      await addCommentToFile(jsFile);
    }

    console.log('All JavaScript files have been processed successfully!');
  } catch (error) {
    console.error('Error adding comments to files:', error);
  }
}

// Run the script
addCommentsToAllFiles();
